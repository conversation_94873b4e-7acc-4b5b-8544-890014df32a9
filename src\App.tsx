import { useState } from "react";
import { AdminSidebar } from "./components/AdminSidebar";
import { DashboardOverview } from "./components/DashboardOverview";
import { ConsultationsManager } from "./components/ConsultationsManager";
import { DoctorsManager } from "./components/DoctorsManager";
import { SlotManagement } from "./components/SlotManagement";
import { Card, CardContent, CardHeader, CardTitle } from "./components/ui/card";

export default function App() {
  const [activeTab, setActiveTab] = useState('dashboard');

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <DashboardOverview />;
      case 'consultations':
        return <ConsultationsManager />;
      case 'doctors':
        return <DoctorsManager />;
      case 'slot-management':
        return <SlotManagement />;
      case 'patients':
        return <PatientsPlaceholder />;
      case 'consultation-types':
        return <ConsultationTypesPlaceholder />;
      case 'analytics':
        return <AnalyticsPlaceholder />;
      case 'revenue':
        return <RevenuePlaceholder />;
      case 'settings':
        return <SettingsPlaceholder />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <div className="flex h-screen bg-background">
      <AdminSidebar activeTab={activeTab} onTabChange={setActiveTab} />
      <main className="flex-1 overflow-auto">
        {renderContent()}
      </main>
    </div>
  );
}

// Placeholder components for tabs not yet implemented
function PatientsPlaceholder() {
  return (
    <div className="p-6">
      <Card>
        <CardHeader>
          <CardTitle>Patients Management</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Patient management features coming soon...</p>
        </CardContent>
      </Card>
    </div>
  );
}

function ConsultationTypesPlaceholder() {
  return (
    <div className="p-6">
      <Card>
        <CardHeader>
          <CardTitle>Consultation Types</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Manage consultation types and pricing...</p>
        </CardContent>
      </Card>
    </div>
  );
}

function AnalyticsPlaceholder() {
  return (
    <div className="p-6">
      <Card>
        <CardHeader>
          <CardTitle>Analytics & Reports</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Detailed analytics and reporting features...</p>
        </CardContent>
      </Card>
    </div>
  );
}

function RevenuePlaceholder() {
  return (
    <div className="p-6">
      <Card>
        <CardHeader>
          <CardTitle>Revenue Management</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Revenue tracking and financial reports...</p>
        </CardContent>
      </Card>
    </div>
  );
}

function SettingsPlaceholder() {
  return (
    <div className="p-6">
      <Card>
        <CardHeader>
          <CardTitle>Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">System settings and configuration...</p>
        </CardContent>
      </Card>
    </div>
  );
}