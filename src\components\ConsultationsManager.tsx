import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Calendar, Search, Filter, Eye, Edit, Trash2, Video, Phone } from "lucide-react";

export function ConsultationsManager() {
  const consultations = [
    {
      id: "CNS001",
      patient: "<PERSON><PERSON>",
      doctor: "Dr. <PERSON><PERSON>",
      date: "2024-01-15",
      time: "10:30 AM",
      type: "General Consultation",
      status: "completed",
      mode: "video",
      fee: "₹800"
    },
    {
      id: "CNS002",
      patient: "<PERSON><PERSON> <PERSON>",
      doctor: "Dr. <PERSON><PERSON>",
      date: "2024-01-15",
      time: "11:00 AM",
      type: "Panchakarma",
      status: "ongoing",
      mode: "video",
      fee: "₹1,200"
    },
    {
      id: "CNS003",
      patient: "<PERSON><PERSON>",
      doctor: "Dr. <PERSON><PERSON><PERSON>",
      date: "2024-01-15",
      time: "11:30 AM",
      type: "Pulse Diagnosis",
      status: "scheduled",
      mode: "phone",
      fee: "₹600"
    },
    {
      id: "CNS004",
      patient: "Rakesh <PERSON>",
      doctor: "Dr. <PERSON>vya Iyer",
      date: "2024-01-15",
      time: "12:00 PM",
      type: "Herbal Medicine",
      status: "cancelled",
      mode: "video",
      fee: "₹900"
    }
  ];

  const getStatusBadge = (status: string) => {
    const variants: { [key: string]: "default" | "secondary" | "destructive" | "outline" } = {
      completed: "default",
      ongoing: "secondary",
      scheduled: "outline",
      cancelled: "destructive"
    };
    return <Badge variant={variants[status] || "outline"}>{status}</Badge>;
  };

  const getModeIcon = (mode: string) => {
    return mode === "video" ? <Video className="h-4 w-4" /> : <Phone className="h-4 w-4" />;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold">Consultations</h1>
          <p className="text-muted-foreground">Manage all consultation bookings</p>
        </div>
        <Button>
          <Calendar className="mr-2 h-4 w-4" />
          Schedule New
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input placeholder="Search patients, doctors..." className="pl-9" />
              </div>
            </div>
            <Select defaultValue="all">
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
                <SelectItem value="ongoing">Ongoing</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
            <Select defaultValue="all">
              <SelectTrigger className="w-[150px]">
                <SelectValue placeholder="Doctor" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Doctors</SelectItem>
                <SelectItem value="dr-rajesh">Dr. Rajesh Kumar</SelectItem>
                <SelectItem value="dr-meera">Dr. Meera Singh</SelectItem>
                <SelectItem value="dr-arjun">Dr. Arjun Nair</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Filter className="mr-2 h-4 w-4" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Consultations Table */}
      <Card>
        <CardHeader>
          <CardTitle>All Consultations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-2">ID</th>
                  <th className="text-left py-3 px-2">Patient</th>
                  <th className="text-left py-3 px-2">Doctor</th>
                  <th className="text-left py-3 px-2">Date & Time</th>
                  <th className="text-left py-3 px-2">Type</th>
                  <th className="text-left py-3 px-2">Mode</th>
                  <th className="text-left py-3 px-2">Fee</th>
                  <th className="text-left py-3 px-2">Status</th>
                  <th className="text-left py-3 px-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                {consultations.map((consultation) => (
                  <tr key={consultation.id} className="border-b hover:bg-muted/50">
                    <td className="py-3 px-2 font-mono text-sm">{consultation.id}</td>
                    <td className="py-3 px-2">{consultation.patient}</td>
                    <td className="py-3 px-2">{consultation.doctor}</td>
                    <td className="py-3 px-2">
                      <div>
                        <div>{consultation.date}</div>
                        <div className="text-sm text-muted-foreground">{consultation.time}</div>
                      </div>
                    </td>
                    <td className="py-3 px-2">{consultation.type}</td>
                    <td className="py-3 px-2">
                      <div className="flex items-center space-x-2">
                        {getModeIcon(consultation.mode)}
                        <span className="capitalize">{consultation.mode}</span>
                      </div>
                    </td>
                    <td className="py-3 px-2">{consultation.fee}</td>
                    <td className="py-3 px-2">{getStatusBadge(consultation.status)}</td>
                    <td className="py-3 px-2">
                      <div className="flex space-x-1">
                        <Button size="sm" variant="ghost">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}