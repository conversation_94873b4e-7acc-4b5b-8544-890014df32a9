import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "./ui/card";
import { Calendar, Users, DollarSign, TrendingUp, Clock, CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { Progress } from "./ui/progress";

export function DashboardOverview() {
  const stats = [
    {
      title: "Total Consultations",
      value: "1,234",
      change: "+12%",
      icon: Calendar,
      color: "text-primary",
      bgColor: "bg-primary/10"
    },
    {
      title: "Active Doctors",
      value: "48",
      change: "+3",
      icon: Users,
      color: "text-green-600",
      bgColor: "bg-green-100"
    },
    {
      title: "Monthly Revenue",
      value: "₹2,45,670",
      change: "+8%",
      icon: DollarSign,
      color: "text-purple-600",
      bgColor: "bg-purple-100"
    },
    {
      title: "Completion Rate",
      value: "94.5%",
      change: "+2.3%",
      icon: TrendingUp,
      color: "text-orange-600",
      bgColor: "bg-orange-100"
    }
  ];

  const recentConsultations = [
    { id: 1, patient: "<PERSON><PERSON>", doctor: "Dr. <PERSON><PERSON>", time: "10:30 AM", status: "completed", type: "General Consultation" },
    { id: 2, patient: "Amit <PERSON>", doctor: "Dr. <PERSON>era <PERSON>", time: "11:00 AM", status: "ongoing", type: "Pancha<PERSON>ma" },
    { id: 3, patient: "<PERSON><PERSON> <PERSON>", doctor: "Dr. Arjun <PERSON>", time: "11:30 AM", status: "scheduled", type: "Pulse Diagnosis" },
    { id: 4, patient: "Rakesh <PERSON>", doctor: "Dr. Kavya Iyer", time: "12:00 PM", status: "cancelled", type: "Herbal Medicine" },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'ongoing': return <Clock className="h-4 w-4 text-primary" />;
      case 'scheduled': return <AlertCircle className="h-4 w-4 text-orange-600" />;
      case 'cancelled': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return null;
    }
  };

  return (
    <div className="p-6 space-y-6">
      <div className="bg-gradient-to-r from-primary/10 via-primary/5 to-transparent p-6 rounded-lg border border-primary/20">
        <h1 className="text-2xl font-semibold text-primary">Dashboard</h1>
        <p className="text-muted-foreground">Welcome to your Ayura admin panel</p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => {
          const Icon = stat.icon;
          return (
            <Card key={stat.title} className="hover:shadow-md transition-shadow border-l-4 border-primary/20 hover:border-primary">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{stat.title}</CardTitle>
                <div className={`p-2 rounded-full ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stat.value}</div>
                <p className="text-xs text-muted-foreground">
                  <span className="text-primary font-medium">{stat.change}</span> from last month
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Consultations */}
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="bg-gradient-to-r from-primary/5 to-transparent">
            <CardTitle className="text-primary">Recent Consultations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentConsultations.map((consultation) => (
                <div key={consultation.id} className="flex items-center justify-between p-3 bg-muted rounded-lg hover:bg-primary/5 transition-colors">
                  <div className="flex-1">
                    <p className="font-medium">{consultation.patient}</p>
                    <p className="text-sm text-muted-foreground">{consultation.doctor} • {consultation.type}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">{consultation.time}</span>
                    {getStatusIcon(consultation.status)}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Doctor Availability */}
        <Card className="hover:shadow-md transition-shadow">
          <CardHeader className="bg-gradient-to-r from-primary/5 to-transparent">
            <CardTitle className="text-primary">Doctor Availability Today</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Available Doctors</span>
                  <span className="font-medium">32/48</span>
                </div>
                <Progress value={67} className="h-3" />
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-green-100 rounded-lg border border-green-200">
                  <span className="text-sm font-medium">Online Now</span>
                  <span className="text-sm font-bold text-green-700 bg-green-200 px-2 py-1 rounded-full">24</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg border border-yellow-200">
                  <span className="text-sm font-medium">On Break</span>
                  <span className="text-sm font-bold text-yellow-700 bg-yellow-200 px-2 py-1 rounded-full">8</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gradient-to-r from-red-50 to-red-100 rounded-lg border border-red-200">
                  <span className="text-sm font-medium">Offline</span>
                  <span className="text-sm font-bold text-red-700 bg-red-200 px-2 py-1 rounded-full">16</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}