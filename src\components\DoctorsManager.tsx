import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "./ui/card";
import { <PERSON><PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import {
  UserPlus,
  Search,
  Star,
  Clock,
  Calendar,
  Edit,
  Trash2,
  Eye,
} from "lucide-react";

export function DoctorsManager() {
  const doctors = [
    {
      id: 1,
      name: "Dr. <PERSON>",
      specialization: "Panchakarma Specialist",
      experience: "15 years",
      rating: 4.8,
      consultations: 234,
      status: "active",
      nextAvailable: "Available now",
      fees: "₹800-1200",
      languages: ["Hindi", "English"],
      image:
        "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 2,
      name: "<PERSON><PERSON><PERSON>",
      specialization: "Ayurvedic Nutrition",
      experience: "12 years",
      rating: 4.9,
      consultations: 189,
      status: "inactive",
      nextAvailable: "2:30 PM today",
      fees: "₹600-900",
      languages: ["Hindi", "English", "Punjabi"],
      image:
        "https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 3,
      name: "Dr. Arjun Nair",
      specialization: "Pulse Diagnosis Expert",
      experience: "20 years",
      rating: 4.7,
      consultations: 312,
      status: "active",
      nextAvailable: "Tomorrow 9:00 AM",
      fees: "₹500-800",
      languages: ["Malayalam", "English", "Tamil"],
      image:
        "https://images.unsplash.com/photo-1582750433449-648ed127bb54?w=150&h=150&fit=crop&crop=face",
    },
    {
      id: 4,
      name: "Dr. Kavya Iyer",
      specialization: "Herbal Medicine",
      experience: "8 years",
      rating: 4.6,
      consultations: 156,
      status: "inactive",
      nextAvailable: "Available now",
      fees: "₹700-1000",
      languages: ["Tamil", "English", "Kannada"],
      image:
        "https://images.unsplash.com/photo-1594824475577-d4b3c5b2c1b3?w=150&h=150&fit=crop&crop=face",
    },
  ];

  const getStatusBadge = (status: string) => {
    const variants: {
      [key: string]: {
        variant: "default" | "secondary" | "destructive" | "outline";
        color: string;
      };
    } = {
      online: { variant: "default", color: "bg-green-100 text-green-800" },
      busy: { variant: "secondary", color: "bg-yellow-100 text-yellow-800" },
      offline: { variant: "outline", color: "bg-red-100 text-red-800" },
    };
    const config = variants[status] || variants.offline;
    return <Badge className={config.color}>{status}</Badge>;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold">Doctors</h1>
          <p className="text-muted-foreground">
            Manage doctor profiles and schedules
          </p>
        </div>
        <Button>
          <UserPlus className="mr-2 h-4 w-4" />
          Add Doctor
        </Button>
      </div>

      {/* Search */}
      <Card>
        <CardContent className="p-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search doctors by name or specialization..."
              className="pl-9"
            />
          </div>
        </CardContent>
      </Card>

      {/* Doctors Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {doctors.map((doctor) => (
          <Card key={doctor.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="w-12 h-12">
                    <AvatarImage src={doctor.image} alt={doctor.name} />
                    <AvatarFallback>
                      {doctor.name
                        .split(" ")
                        .map((n) => n[0])
                        .join("")}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <h3 className="font-semibold">{doctor.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {doctor.specialization}
                    </p>
                  </div>
                </div>
                {getStatusBadge(doctor.status)}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">Experience</p>
                  <p className="font-medium">{doctor.experience}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Rating</p>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{doctor.rating}</span>
                  </div>
                </div>
                <div>
                  <p className="text-muted-foreground">Consultations</p>
                  <p className="font-medium">{doctor.consultations}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">Fees</p>
                  <p className="font-medium">{doctor.fees}</p>
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Next Available</p>
                <div className="flex items-center space-x-1 mt-1">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{doctor.nextAvailable}</span>
                </div>
              </div>

              <div>
                <p className="text-sm text-muted-foreground">Languages</p>
                <div className="flex flex-wrap gap-1 mt-1">
                  {doctor.languages.map((lang) => (
                    <Badge key={lang} variant="outline" className="text-xs">
                      {lang}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex space-x-2 pt-2">
                <Button size="sm" variant="outline" className="flex-1">
                  <Eye className="mr-1 h-3 w-3" />
                  View
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <Calendar className="mr-1 h-3 w-3" />
                  Schedule
                </Button>
                <Button size="sm" variant="outline">
                  <Edit className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline">
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
