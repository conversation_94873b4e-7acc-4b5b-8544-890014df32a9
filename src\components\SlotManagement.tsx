import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "./ui/card";
import { But<PERSON> } from "./ui/button";
import { Badge } from "./ui/badge";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Calendar } from "./ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "./ui/popover";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Switch } from "./ui/switch";
import { 
  Plus, 
  Calendar as CalendarIcon, 
  Clock, 
  Edit, 
  Trash2, 
  Copy,
  Filter,
  Search,
  CalendarDays,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle
} from "lucide-react";
import { format } from "date-fns";

export function SlotManagement() {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [isAddSlotOpen, setIsAddSlotOpen] = useState(false);

  const doctors = [
    { id: "1", name: "Dr. Rajesh Kumar", specialization: "Panchakarma Specialist" },
    { id: "2", name: "Dr. Meera Singh", specialization: "Ayurvedic Nutrition" },
    { id: "3", name: "Dr. Arjun Nair", specialization: "Pulse Diagnosis Expert" },
    { id: "4", name: "Dr. Kavya Iyer", specialization: "Herbal Medicine" },
  ];

  const slots = [
    {
      id: "SLT001",
      doctor: "Dr. Rajesh Kumar",
      date: "2024-01-15",
      time: "09:00 AM",
      duration: 30,
      status: "available",
      consultationType: "General Consultation",
      fee: "₹800"
    },
    {
      id: "SLT002",
      doctor: "Dr. Rajesh Kumar",
      date: "2024-01-15",
      time: "09:30 AM",
      duration: 30,
      status: "booked",
      consultationType: "General Consultation",
      fee: "₹800",
      patient: "Priya Sharma"
    },
    {
      id: "SLT003",
      doctor: "Dr. Meera Singh",
      date: "2024-01-15",
      time: "10:00 AM",
      duration: 45,
      status: "available",
      consultationType: "Panchakarma",
      fee: "₹1200"
    },
    {
      id: "SLT004",
      doctor: "Dr. Arjun Nair",
      date: "2024-01-15",
      time: "11:00 AM",
      duration: 30,
      status: "blocked",
      consultationType: "Pulse Diagnosis",
      fee: "₹600"
    },
  ];

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      available: { variant: "outline" as const, color: "bg-green-100 text-green-800", icon: CheckCircle },
      booked: { variant: "default" as const, color: "bg-blue-100 text-blue-800", icon: Users },
      blocked: { variant: "destructive" as const, color: "bg-red-100 text-red-800", icon: XCircle },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.available;
    const Icon = config.icon;
    
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {status}
      </Badge>
    );
  };

  const timeSlots = [
    "09:00 AM", "09:30 AM", "10:00 AM", "10:30 AM", "11:00 AM", "11:30 AM",
    "12:00 PM", "12:30 PM", "01:00 PM", "01:30 PM", "02:00 PM", "02:30 PM",
    "03:00 PM", "03:30 PM", "04:00 PM", "04:30 PM", "05:00 PM", "05:30 PM"
  ];

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold">Slot Management</h1>
          <p className="text-muted-foreground">Create and manage appointment slots for doctors</p>
        </div>
        <Dialog open={isAddSlotOpen} onOpenChange={setIsAddSlotOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Slot
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Appointment Slot</DialogTitle>
            </DialogHeader>
            <AddSlotForm doctors={doctors} onClose={() => setIsAddSlotOpen(false)} />
          </DialogContent>
        </Dialog>
      </div>

      <Tabs defaultValue="slots" className="space-y-6">
        <TabsList>
          <TabsTrigger value="slots">All Slots</TabsTrigger>
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="bulk">Bulk Create</TabsTrigger>
        </TabsList>

        <TabsContent value="slots" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-wrap gap-4">
                <div className="flex-1 min-w-[200px]">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input placeholder="Search slots..." className="pl-9" />
                  </div>
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Doctor" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Doctors</SelectItem>
                    {doctors.map((doctor) => (
                      <SelectItem key={doctor.id} value={doctor.id}>
                        {doctor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="available">Available</SelectItem>
                    <SelectItem value="booked">Booked</SelectItem>
                    <SelectItem value="blocked">Blocked</SelectItem>
                  </SelectContent>
                </Select>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={selectedDate}
                      onSelect={setSelectedDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </CardContent>
          </Card>

          {/* Slots Table */}
          <Card>
            <CardHeader>
              <CardTitle>Appointment Slots</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-2">Slot ID</th>
                      <th className="text-left py-3 px-2">Doctor</th>
                      <th className="text-left py-3 px-2">Date & Time</th>
                      <th className="text-left py-3 px-2">Duration</th>
                      <th className="text-left py-3 px-2">Type</th>
                      <th className="text-left py-3 px-2">Fee</th>
                      <th className="text-left py-3 px-2">Status</th>
                      <th className="text-left py-3 px-2">Patient</th>
                      <th className="text-left py-3 px-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {slots.map((slot) => (
                      <tr key={slot.id} className="border-b hover:bg-muted/50">
                        <td className="py-3 px-2 font-mono text-sm">{slot.id}</td>
                        <td className="py-3 px-2">{slot.doctor}</td>
                        <td className="py-3 px-2">
                          <div>
                            <div>{slot.date}</div>
                            <div className="text-sm text-muted-foreground">{slot.time}</div>
                          </div>
                        </td>
                        <td className="py-3 px-2">{slot.duration} min</td>
                        <td className="py-3 px-2">{slot.consultationType}</td>
                        <td className="py-3 px-2">{slot.fee}</td>
                        <td className="py-3 px-2">{getStatusBadge(slot.status)}</td>
                        <td className="py-3 px-2">{slot.patient || "-"}</td>
                        <td className="py-3 px-2">
                          <div className="flex space-x-1">
                            <Button size="sm" variant="ghost">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="ghost">
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button size="sm" variant="ghost">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="calendar" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Calendar View</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center text-muted-foreground py-8">
                <CalendarDays className="mx-auto h-12 w-12 mb-4" />
                <p>Calendar view coming soon...</p>
                <p className="text-sm">Visual representation of all slots across dates</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Bulk Slot Creation</CardTitle>
            </CardHeader>
            <CardContent>
              <BulkSlotForm doctors={doctors} timeSlots={timeSlots} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

function AddSlotForm({ doctors, onClose }: { doctors: any[], onClose: () => void }) {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Doctor</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select doctor" />
            </SelectTrigger>
            <SelectContent>
              {doctors.map((doctor) => (
                <SelectItem key={doctor.id} value={doctor.id}>
                  {doctor.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label>Consultation Type</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="general">General Consultation</SelectItem>
              <SelectItem value="panchakarma">Panchakarma</SelectItem>
              <SelectItem value="pulse">Pulse Diagnosis</SelectItem>
              <SelectItem value="herbal">Herbal Medicine</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="w-full justify-start">
                <CalendarIcon className="mr-2 h-4 w-4" />
                {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
        <div className="space-y-2">
          <Label>Time</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select time" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="09:00">09:00 AM</SelectItem>
              <SelectItem value="09:30">09:30 AM</SelectItem>
              <SelectItem value="10:00">10:00 AM</SelectItem>
              <SelectItem value="10:30">10:30 AM</SelectItem>
              <SelectItem value="11:00">11:00 AM</SelectItem>
              <SelectItem value="11:30">11:30 AM</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Duration (minutes)</Label>
          <Select defaultValue="30">
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="15">15 minutes</SelectItem>
              <SelectItem value="30">30 minutes</SelectItem>
              <SelectItem value="45">45 minutes</SelectItem>
              <SelectItem value="60">60 minutes</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label>Fee</Label>
          <Input placeholder="₹800" />
        </div>
      </div>

      <div className="flex justify-end space-x-3">
        <Button variant="outline" onClick={onClose}>Cancel</Button>
        <Button onClick={onClose}>Create Slot</Button>
      </div>
    </div>
  );
}

function BulkSlotForm({ doctors, timeSlots }: { doctors: any[], timeSlots: string[] }) {
  const [selectedDates, setSelectedDates] = useState<Date[]>([]);
  
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label>Doctor</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select doctor" />
            </SelectTrigger>
            <SelectContent>
              {doctors.map((doctor) => (
                <SelectItem key={doctor.id} value={doctor.id}>
                  {doctor.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label>Consultation Type</Label>
          <Select>
            <SelectTrigger>
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="general">General Consultation</SelectItem>
              <SelectItem value="panchakarma">Panchakarma</SelectItem>
              <SelectItem value="pulse">Pulse Diagnosis</SelectItem>
              <SelectItem value="herbal">Herbal Medicine</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <Label>Select Dates</Label>
        <Card className="p-4">
          <Calendar
            mode="multiple"
            selected={selectedDates}
            onSelect={(dates) => setSelectedDates(dates || [])}
            className="rounded-md border"
          />
        </Card>
      </div>

      <div className="space-y-2">
        <Label>Time Slots</Label>
        <Card className="p-4">
          <div className="grid grid-cols-3 gap-2">
            {timeSlots.map((time) => (
              <div key={time} className="flex items-center space-x-2">
                <input type="checkbox" id={time} className="rounded" />
                <Label htmlFor={time} className="text-sm">{time}</Label>
              </div>
            ))}
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-3 gap-4">
        <div className="space-y-2">
          <Label>Duration (minutes)</Label>
          <Select defaultValue="30">
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="15">15 minutes</SelectItem>
              <SelectItem value="30">30 minutes</SelectItem>
              <SelectItem value="45">45 minutes</SelectItem>
              <SelectItem value="60">60 minutes</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label>Fee</Label>
          <Input placeholder="₹800" />
        </div>
        <div className="space-y-2">
          <Label>Repeat Weekly</Label>
          <div className="flex items-center space-x-2 pt-2">
            <Switch />
            <span className="text-sm text-muted-foreground">Create for next 4 weeks</span>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Create {selectedDates.length} × {timeSlots.length} Slots
        </Button>
      </div>
    </div>
  );
}